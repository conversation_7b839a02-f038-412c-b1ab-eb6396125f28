('C:\\Users\\<USER>\\Desktop\\wechat\\build\\WeChatApp\\PYZ-00.pyz',
 [('__future__', 'D:\\Python\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\Python\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_colorize', 'D:\\Python\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\Python\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\Python\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support', 'D:\\Python\\Lib\\_ios_support.py', 'PYMODULE'),
  ('_opcode_metadata', 'D:\\Python\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Python\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\Python\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Python\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyrepl', 'D:\\Python\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'D:\\Python\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'D:\\Python\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.commands', 'D:\\Python\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'D:\\Python\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console', 'D:\\Python\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('_pyrepl.curses', 'D:\\Python\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'D:\\Python\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'D:\\Python\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input', 'D:\\Python\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'D:\\Python\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.main', 'D:\\Python\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl.pager', 'D:\\Python\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('_pyrepl.reader', 'D:\\Python\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl.readline', 'D:\\Python\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'D:\\Python\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace', 'D:\\Python\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.types', 'D:\\Python\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.unix_console',
   'D:\\Python\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'D:\\Python\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'D:\\Python\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_pyrepl.windows_console',
   'D:\\Python\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_sitebuiltins', 'D:\\Python\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'D:\\Python\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\Python\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\Python\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Python\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\Python\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Python\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Python\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Python\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks', 'D:\\Python\\Lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.constants', 'D:\\Python\\Lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.coroutines', 'D:\\Python\\Lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.events', 'D:\\Python\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions', 'D:\\Python\\Lib\\asyncio\\exceptions.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Python\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'D:\\Python\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\Python\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\Python\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\Python\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Python\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'D:\\Python\\Lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.queues', 'D:\\Python\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\Python\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Python\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'D:\\Python\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered', 'D:\\Python\\Lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.streams', 'D:\\Python\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess', 'D:\\Python\\Lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.taskgroups', 'D:\\Python\\Lib\\asyncio\\taskgroups.py', 'PYMODULE'),
  ('asyncio.tasks', 'D:\\Python\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'D:\\Python\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'D:\\Python\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports', 'D:\\Python\\Lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.trsock', 'D:\\Python\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Python\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Python\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Python\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\Python\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\Python\\Lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bz2', 'D:\\Python\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Python\\Lib\\calendar.py', 'PYMODULE'),
  ('click',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('code', 'D:\\Python\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Python\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent', 'D:\\Python\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\Python\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Python\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Python\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Python\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'D:\\Python\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\Python\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\Python\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\Python\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Python\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\Python\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\Python\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\Python\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Python\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Python\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Python\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Python\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\Python\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'D:\\Python\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('curses', 'D:\\Python\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'D:\\Python\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('dataclasses', 'D:\\Python\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\Python\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\Python\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\Python\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\Python\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\Python\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Python\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Python\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\Python\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'D:\\Python\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\Python\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\Python\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\Python\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\Python\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\Python\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\Python\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\Python\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\Python\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Python\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\Python\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\Python\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\Python\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\Python\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\Python\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\Python\\Lib\\email\\utils.py', 'PYMODULE'),
  ('flask',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.scaffold',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask_cors',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask_cors\\__init__.py',
   'PYMODULE'),
  ('flask_cors.core',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask_cors\\core.py',
   'PYMODULE'),
  ('flask_cors.decorator',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask_cors\\decorator.py',
   'PYMODULE'),
  ('flask_cors.extension',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask_cors\\extension.py',
   'PYMODULE'),
  ('flask_cors.version',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask_cors\\version.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Python\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\Python\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\Python\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Python\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Python\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Python\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\Python\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\Python\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Python\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Python\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\Python\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\Python\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'D:\\Python\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\Python\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\Python\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.server', 'D:\\Python\\Lib\\http\\server.py', 'PYMODULE'),
  ('importlib', 'D:\\Python\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\Python\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Python\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Python\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\Python\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Python\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Python\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Python\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Python\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Python\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Python\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Python\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Python\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers', 'D:\\Python\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources',
   'D:\\Python\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Python\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Python\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\Python\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Python\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Python\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Python\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\Python\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\Python\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Python\\Lib\\ipaddress.py', 'PYMODULE'),
  ('itsdangerous',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jinja2',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'D:\\Python\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\Python\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\Python\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\Python\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\Python\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\Python\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'D:\\Python\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\Python\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Python\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Python\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Python\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Python\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Python\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Python\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Python\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Python\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Python\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Python\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Python\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Python\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Python\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Python\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Python\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Python\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Python\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Python\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Python\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Python\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Python\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Python\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\Python\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\Python\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\Python\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\Python\\Lib\\opcode.py', 'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Python\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._abc', 'D:\\Python\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('pathlib._local', 'D:\\Python\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('pickle', 'D:\\Python\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Python\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\Python\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\Python\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\Python\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'D:\\Python\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\Python\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics', 'D:\\Python\\Lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pymysql',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql._auth',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.charset',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.connections',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.constants',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.converters',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.err',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.times',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('queue', 'D:\\Python\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Python\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Python\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter', 'D:\\Python\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'D:\\Python\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\Python\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\Python\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\Python\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Python\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Python\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\Python\\Lib\\site.py', 'PYMODULE'),
  ('socket', 'D:\\Python\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\Python\\Lib\\socketserver.py', 'PYMODULE'),
  ('ssl', 'D:\\Python\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\Python\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\Python\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Python\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Python\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\Python\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('tarfile', 'D:\\Python\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Python\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Python\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Python\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\Python\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Python\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'D:\\Python\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'D:\\Python\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'D:\\Python\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'D:\\Python\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\Python\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\Python\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\Python\\Lib\\typing.py', 'PYMODULE'),
  ('unittest', 'D:\\Python\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'D:\\Python\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\Python\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'D:\\Python\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'D:\\Python\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'D:\\Python\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'D:\\Python\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'D:\\Python\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'D:\\Python\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'D:\\Python\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'D:\\Python\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'D:\\Python\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'D:\\Python\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\Python\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\Python\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\Python\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\Python\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('uuid', 'D:\\Python\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'D:\\Python\\Lib\\webbrowser.py', 'PYMODULE'),
  ('werkzeug',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('xml', 'D:\\Python\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers', 'D:\\Python\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', 'D:\\Python\\Lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.sax', 'D:\\Python\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Python\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Python\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'D:\\Python\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\Python\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader', 'D:\\Python\\Lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\Python\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\Python\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'D:\\Python\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path', 'D:\\Python\\Lib\\zipfile\\_path\\__init__.py', 'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Python\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'D:\\Python\\Lib\\zipimport.py', 'PYMODULE')])
