// 通讯录数据
let contacts = [];
let allContacts = []; // 保存所有联系人的原始数据
let filteredContacts = []; // 搜索过滤后的联系人
let currentFilter = 'all'; // 当前过滤器
let searchQuery = ''; // 当前搜索关键词

// 下拉刷新实例
let contactsPullToRefresh = null;
// 防止重复初始化的标志
let contactsInitialized = false;
// 将初始化标志暴露到全局，以便登录后重置
window.contactsInitialized = contactsInitialized;
window.currentUser = window.currentUser || null;


// 外部点击/按键 关闭搜索框的处理器引用，避免重复绑定
let searchOutsideClickHandler = null;
let searchEscapeHandler = null;

let searchMouseLeaveHandler = null;

// 初始化通讯录页面
function initContacts() {
    // 防止重复初始化，但检查全局标志以支持登录后重新初始化
    if (contactsInitialized && window.contactsInitialized !== false) {
        return;
    }
    contactsInitialized = true;
    window.contactsInitialized = true;

    // 检查contacts-list元素是否存在
    const contactsList = document.getElementById('contacts-list');
    if (!contactsList) {
        // 尝试重新加载联系人页面
        fetch('partials/contacts.html')
            .then(response => response.text())
            .then(html => {
                const contactsPage = document.getElementById('contacts-page');
                if (contactsPage) {
                    contactsPage.innerHTML = html;
                    // 初始化搜索功能
                    initSearchFeatures();
                    // 从服务器获取通讯录数据
                    fetchContacts();
                    // 初始化下拉刷新功能
                    initContactsPullToRefresh();
                    // 初始化WebSocket监听器
                    initContactsWebSocketListeners();
                    // 初始化头部按钮事件
                    initContactsHeaderButtons();
                    // 初始化分组标题位置
                    initSectionHeaderPosition();
                    // 初始化删除好友弹窗
                    initFriendDeleteModal();
                }
            })
            .catch(error => {
                console.error('加载联系人页面失败:', error);
            });
        return;
    }

    // 初始化搜索功能
    initSearchFeatures();
    // 从服务器获取通讯录数据
    fetchContacts();
    // 初始化下拉刷新功能
    initContactsPullToRefresh();
    // 初始化WebSocket监听器
    initContactsWebSocketListeners();
    // 初始化头部按钮事件
    initContactsHeaderButtons();
    // 初始化分组标题位置
    initSectionHeaderPosition();
    // 初始化删除好友弹窗
    initFriendDeleteModal();
}

// 初始化搜索功能
function initSearchFeatures() {
    const searchInput = document.getElementById('contacts-search-input');
    const searchClearBtn = document.getElementById('search-clear-btn');
    // 搜索输入事件
    if (searchInput) {
        searchInput.addEventListener('input', handleSearchInput);
        searchInput.addEventListener('focus', () => {
            searchInput.parentElement.classList.add('focused');
        });
        searchInput.addEventListener('blur', () => {
            searchInput.parentElement.classList.remove('focused');
        });
    }
}

// 从服务器获取通讯录数据
function fetchContacts() {
    fetch('/api/contacts', {
        credentials: 'include'
    })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                window.currentUser = data.data.current_user;
                processContactsData(data.data);
            } else {
                console.error('获取通讯录失败:', data.msg);
            }
        })
        .catch(error => {
            console.error('获取通讯录错误:', error);
        });
}

// 搜索防抖定时器
let searchTimeout = null;

// 搜索输入处理
function handleSearchInput(e) {
    searchQuery = e.target.value.trim();
    const searchClearBtn = document.getElementById('search-clear-btn');

    if (searchQuery) {
        // 移除清除图标：如元素存在，保持隐藏
        if (searchClearBtn) {
            searchClearBtn.style.display = 'none';
            searchClearBtn.classList.remove('show');
        }

        // 清除之前的定时器
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // 设置防抖，500ms后执行搜索
        searchTimeout = setTimeout(() => {
            searchUsers(searchQuery);
        }, 500);
    } else {
        if (searchClearBtn) {
            searchClearBtn.style.display = 'none';
            searchClearBtn.classList.remove('show');
        }

        // 清除搜索定时器
        if (searchTimeout) {
            clearTimeout(searchTimeout);
            searchTimeout = null;
        }

        // 如果没有搜索内容，显示好友列表
        applyFilters();
    }
}

// 清除搜索（保留函数供其他地方调用，即使界面已无“清除搜索”按钮）
function clearSearch() {
    const searchInput = document.getElementById('contacts-search-input');

    if (searchInput) {
        searchInput.value = '';
        searchInput.focus();
    }

    searchQuery = '';
    // 清除搜索结果，显示好友列表
    filteredContacts = [];
    applyFilters();
}

// 搜索用户函数
function searchUsers(query) {
    if (!query || query.length < 1) {
        return;
    }

    fetch(`/api/users/search?q=${encodeURIComponent(query)}`, {
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            // 将搜索结果转换为联系人格式
            const searchResults = data.data.map(user => ({
                id: user.id,
                name: user.username,
                wechatId: user.wechat_id || `wx_${user.id}`,
                avatar: user.avatar || `https://picsum.photos/seed/user${user.id}/100/100`,
                isOnline: false,
                isRecent: false,
                isFavorite: false,
                isSearchResult: true,
                isFriend: user.is_friend
            }));

            filteredContacts = searchResults;
            updateSearchResults();
            renderSearchResults();
        } else {
            console.error('搜索用户失败:', data.msg);
            showToast(data.msg, 'error');
        }
    })
    .catch(error => {
        console.error('搜索用户错误:', error);
        showToast('搜索失败，请重试', 'error');
    });
}

// 应用过滤器
function applyFilters() {
    if (searchQuery) {
        // 如果有搜索关键词，显示搜索结果
        updateSearchResults();
        renderFilteredContacts();
    } else {
        // 如果没有搜索关键词，显示好友列表
        filteredContacts = [];
        updateSearchResults();
        renderContacts();
    }
}

// 更新搜索结果信息
function updateSearchResults() {
    const searchResultsInfo = document.getElementById('search-results-info');
    const searchResultsCount = document.getElementById('search-results-count');
    const noResults = document.getElementById('no-results');

    if (searchQuery) {
        if (searchResultsInfo && searchResultsCount) {
            searchResultsInfo.style.display = 'flex';
            searchResultsCount.textContent = `找到 ${filteredContacts.length} 个结果`;
        }

        if (noResults) {
            noResults.style.display = filteredContacts.length === 0 ? 'block' : 'none';
        }
    } else {
        if (searchResultsInfo) {
            searchResultsInfo.style.display = 'none';
        }
        if (noResults) {
            noResults.style.display = 'none';
        }
    }
}

// 渲染过滤后的联系人
function renderFilteredContacts() {
    if (searchQuery) {
        // 显示搜索结果
        renderSearchResults();
    } else {
        // 显示正常的分组联系人
        renderContacts();
    }
}

// 渲染搜索结果
function renderSearchResults() {
    const contactsList = document.getElementById('contacts-list');
    if (!contactsList) {
        return;
    }

    contactsList.innerHTML = '';

    if (filteredContacts.length === 0) {
        return;
    }

    // 创建搜索结果分组
    const sectionElement = document.createElement('div');
    sectionElement.className = 'contact-section';

    const headerElement = document.createElement('div');
    headerElement.className = 'section-header';
    headerElement.textContent = '搜索结果';
    sectionElement.appendChild(headerElement);

    filteredContacts.forEach(contact => {
        const contactElement = createContactElement(contact);
        sectionElement.appendChild(contactElement);
    });

    contactsList.appendChild(sectionElement);
}

// 处理通讯录数据
function processContactsData(data) {
    contacts = [];
    allContacts = [];

    // “我”分组
    if (data.current_user) {
        const myContact = {
            id: data.current_user.id,
            name: data.current_user.username + ' (我)',
            wechatId: data.current_user.wechat_id || `wx_${data.current_user.id}`,
            avatar: data.current_user.avatar || `https://picsum.photos/seed/user${data.current_user.id}/100/100`,
            isOnline: true,
            isRecent: true,
            isFavorite: false,
            isSearchResult: false,
            isFriend: true
        };

        contacts.push({
            letter: '我',
            items: [myContact]
        });
        allContacts.push(myContact);
    }
    // 好友分组（现在contacts只包含好友）
    let friends = data.contacts || [];
    let byLetter = {};

    friends.forEach(user => {
        const contact = {
            id: user.id,
            name: user.username,
            wechatId: user.wechat_id || `wx_${user.id}`,
            avatar: user.avatar || `https://picsum.photos/seed/user${user.id}/100/100`,
            isOnline: false, // 默认离线，通过WebSocket和API实时更新真实状态
            isRecent: Math.random() > 0.7, // 模拟最近联系
            isFavorite: Math.random() > 0.9, // 模拟星标朋友
            isSearchResult: false,
            isFriend: true // 这些都是好友
        };

        allContacts.push(contact);

        let name = user.username;
        let letter = getFirstLetter(name);
        // 简单拼音首字母处理
        if (/[ -\u007f]/.test(letter)) {
            // 英文或数字
            if (!/[A-Z]/.test(letter)) letter = '#';
        } else if (/[-\uffff]/.test(letter)) {
            // 其它字符归#
            letter = '#';
        } else if (/[-\uffff]/.test(letter)) {
            letter = '#';
        } else if (/[-\uffff]/.test(letter)) {
            letter = '#';
        } else if (/[-\uffff]/.test(letter)) {
            letter = '#';
        } else if (/[-\uffff]/.test(letter)) {
            letter = '#';
        } else if (/[-\uffff]/.test(letter)) {
            letter = '#';
        } else if (/[-\uffff]/.test(letter)) {
            letter = '#';
        } else if (/[-\uffff]/.test(letter)) {
            letter = '#';
        } else if (/[-\uffff]/.test(letter)) {
            letter = '#';
        } else if (/[-\uffff]/.test(letter)) {
            letter = '#';
        } else if (/[-\uffff]/.test(letter)) {
            letter = '#';
        } else if (/[\u4e00-\u9fa5]/.test(letter)) {
            // 中文首字母拼音分组（简化）
            const pinyinMap = {
                '阿': 'A', '爱': 'A', '安': 'A',
                '百': 'B', '白': 'B', '北': 'B', '贝': 'B',
                '陈': 'C', '楚': 'C', '长': 'C',
                '董': 'D', '杜': 'D', '大': 'D',
                '俄': 'E', '鄂': 'E',
                '冯': 'F', '范': 'F', '方': 'F',
                '高': 'G', '郭': 'G', '甘': 'G',
                '黄': 'H', '胡': 'H', '何': 'H',
                '吉': 'J', '贾': 'J', '金': 'J',
                '柯': 'K', '孔': 'K',
                '李': 'L', '刘': 'L', '林': 'L',
                '马': 'M', '梅': 'M', '孟': 'M',
                '牛': 'N', '农': 'N',
                '欧': 'O',
                '彭': 'P', '潘': 'P',
                '钱': 'Q', '秦': 'Q',
                '任': 'R', '阮': 'R',
                '孙': 'S', '宋': 'S', '苏': 'S',
                '唐': 'T', '汤': 'T', '田': 'T',
                '王': 'W', '吴': 'W', '魏': 'W', '微': 'W',
                '徐': 'X', '许': 'X', '夏': 'X',
                '杨': 'Y', '于': 'Y', '袁': 'Y',
                '张': 'Z', '赵': 'Z', '钟': 'Z'
            };
            letter = pinyinMap[name[0]] || '#';
        } else {
            letter = '#';
        }
        if (!byLetter[letter]) byLetter[letter] = [];
        byLetter[letter].push(contact);
    });

    Object.keys(byLetter).sort().forEach(letter => {
        contacts.push({ letter, items: byLetter[letter] });
    });

    // 更新联系人数量
    updateContactsCount();

    // 生成字母索引
    generateAlphabetIndex();

    renderContacts();

    // 初始化分组标题位置
    setTimeout(() => {
        initSectionHeaderPosition();
    }, 50);

    // 立即获取在线状态
    setTimeout(() => {
        fetchContactsOnlineStatus();
    }, 100);
}

// 获取首字母
function getFirstLetter(name) {
    let letter = name[0].toUpperCase();

    if (/[A-Z]/.test(letter)) {
        return letter;
    } else if (/[\u4e00-\u9fa5]/.test(letter)) {
        // 中文首字母拼音分组（简化）
        const pinyinMap = {
            '阿': 'A', '爱': 'A', '安': 'A',
            '百': 'B', '白': 'B', '北': 'B', '贝': 'B',
            '陈': 'C', '楚': 'C', '长': 'C',
            '董': 'D', '杜': 'D', '大': 'D',
            '俄': 'E', '鄂': 'E',
            '冯': 'F', '范': 'F', '方': 'F',
            '高': 'G', '郭': 'G', '甘': 'G',
            '黄': 'H', '胡': 'H', '何': 'H',
            '吉': 'J', '贾': 'J', '金': 'J',
            '柯': 'K', '孔': 'K',
            '李': 'L', '刘': 'L', '林': 'L',
            '马': 'M', '梅': 'M', '孟': 'M',
            '牛': 'N', '农': 'N',
            '欧': 'O',
            '彭': 'P', '潘': 'P',
            '钱': 'Q', '秦': 'Q',
            '任': 'R', '阮': 'R',
            '孙': 'S', '宋': 'S', '苏': 'S',
            '唐': 'T', '汤': 'T', '田': 'T',
            '王': 'W', '吴': 'W', '魏': 'W', '微': 'W',
            '徐': 'X', '许': 'X', '夏': 'X',
            '杨': 'Y', '于': 'Y', '袁': 'Y',
            '张': 'Z', '赵': 'Z', '钟': 'Z'
        };
        return pinyinMap[name[0]] || '#';
    } else {
        return '#';
    }
}

// 更新联系人数量
function updateContactsCount() {
    const contactsCount = document.getElementById('contacts-count');
    if (contactsCount) {
        contactsCount.textContent = `${allContacts.length} 位联系人`;
    }
}

// 生成字母索引
function generateAlphabetIndex() {
    const alphabetIndex = document.getElementById('alphabet-index');
    if (!alphabetIndex) return;

    alphabetIndex.innerHTML = '';

    // 获取所有存在的字母
    const letters = contacts.map(section => section.letter).filter(letter => letter !== '我');

    letters.forEach(letter => {
        const letterElement = document.createElement('div');
        letterElement.className = 'index-letter';
        letterElement.textContent = letter;
        letterElement.setAttribute('data-letter', letter);

        letterElement.addEventListener('click', () => {
            scrollToSection(letter);
        });

        alphabetIndex.appendChild(letterElement);
    });
}

// 滚动到指定分组
function scrollToSection(letter) {
    const sections = document.querySelectorAll('.section-header');
    for (let section of sections) {
        if (section.textContent === letter) {
            section.scrollIntoView({ behavior: 'smooth', block: 'start' });
            break;
        }
    }
}

// 渲染通讯录
function renderContacts() {
    const contactsList = document.getElementById('contacts-list');
    if (!contactsList) {
        return;
    }

    contactsList.innerHTML = '';

    if (contacts.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-contacts-message';
        emptyMessage.innerHTML = `
            <div class="empty-icon">
                <i class="fas fa-user-friends"></i>
            </div>
            <div class="empty-title">暂无好友</div>
            <div class="empty-subtitle">点击右上角的 <i class="fas fa-search"></i> 按钮搜索并添加好友</div>
        `;
        contactsList.appendChild(emptyMessage);
        return;
    }
    contacts.forEach(section => {
        const sectionElement = createContactSection(section);
        contactsList.appendChild(sectionElement);
    });

    // 显示移动端操作提示
    showMobileTipsIfNeeded();
}

// 创建通讯录分组
function createContactSection(section) {
    const sectionElement = document.createElement('div');
    sectionElement.className = 'contact-section';

    const headerElement = document.createElement('div');
    headerElement.className = 'section-header';
    headerElement.textContent = section.letter;

    sectionElement.appendChild(headerElement);

    section.items.forEach(contact => {
        const contactElement = createContactElement(contact);
        sectionElement.appendChild(contactElement);
    });

    return sectionElement;
}

// 创建联系人元素
function createContactElement(contact) {
    const contactElement = document.createElement('div');
    contactElement.className = 'contact-item';
    contactElement.setAttribute('data-contact-id', contact.id);

    const avatarElement = document.createElement('div');
    avatarElement.className = 'contact-avatar';
    avatarElement.style.backgroundImage = `url(${contact.avatar})`;

    const contactInfoElement = document.createElement('div');
    contactInfoElement.className = 'contact-info';

    const nameElement = document.createElement('div');
    nameElement.className = 'contact-name';
    nameElement.textContent = contact.name;

    const wechatIdElement = document.createElement('div');
    wechatIdElement.className = 'contact-wechat-id';
    wechatIdElement.textContent = `微信号: ${contact.wechatId}`;

    const statusElement = document.createElement('div');
    statusElement.className = 'contact-status';

    const statusDot = document.createElement('div');
    statusDot.className = `status-dot ${contact.isOnline ? '' : 'offline'}`;

    const statusText = document.createElement('span');
    statusText.textContent = contact.isOnline ? '在线' : '离线';

    statusElement.appendChild(statusDot);
    statusElement.appendChild(statusText);

    // 添加星标标识
    if (contact.isFavorite) {
        const starIcon = document.createElement('i');
        starIcon.className = 'fas fa-star';
        starIcon.style.color = '#ffc107';
        starIcon.style.marginLeft = '8px';
        starIcon.style.fontSize = '12px';
        statusElement.appendChild(starIcon);
    }

    contactInfoElement.appendChild(nameElement);
    contactInfoElement.appendChild(wechatIdElement);
    contactInfoElement.appendChild(statusElement);

    contactElement.appendChild(avatarElement);
    contactElement.appendChild(contactInfoElement);

    // 如果是搜索结果，添加操作按钮
    if (contact.isSearchResult) {
        const actionsElement = document.createElement('div');
        actionsElement.className = 'contact-actions';

        if (contact.isFriend) {
            // 已经是好友，显示"已添加"状态
            const addedLabel = document.createElement('span');
            addedLabel.className = 'friend-status added';
            addedLabel.textContent = '已添加';
            actionsElement.appendChild(addedLabel);
        } else {
            // 不是好友，显示"添加好友"按钮
            const addButton = document.createElement('button');
            addButton.className = 'add-friend-btn';
            addButton.innerHTML = '<i class="fas fa-user-plus"></i> 添加好友';
            addButton.addEventListener('click', (e) => {
                e.stopPropagation();
                addFriend(contact.id, contact.name, addButton);
            });
            actionsElement.appendChild(addButton);
        }

        contactElement.appendChild(actionsElement);
    } else {
        // 如果是好友列表中的联系人，添加右键删除功能
        contactElement.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            showDeleteFriendMenu(e, contact);
        });

        // 为移动端添加滑动删除功能
        addSwipeDeleteFeature(contactElement, contact);
    }

    // 点击打开聊天（但不包括操作按钮区域）
    contactElement.addEventListener('click', (e) => {
        if (!e.target.closest('.contact-actions')) {
            openChatWithContact(contact);
        }
    });

    return contactElement;
}

// 确保聊天模块已初始化的辅助函数
function ensureChatModuleInitialized() {
    return new Promise((resolve, reject) => {
        if (typeof startNewChat === 'function') {
            resolve();
            return;
        }

        // 检查是否有initChat函数
        if (typeof initChat !== 'function') {
            console.error('initChat 函数不存在，尝试备用初始化方法');
            // 尝试备用初始化方法
            tryFallbackInitialization();
        } else {
            try {
                // 调用初始化函数
                initChat();
            } catch (error) {
                console.error('调用 initChat 时发生错误:', error);
                tryFallbackInitialization();
            }
        }

        // 备用初始化方法
        function tryFallbackInitialization() {
            // 直接确保关键函数可用
            if (typeof window.startNewChat !== 'function' && typeof startNewChat === 'function') {
                window.startNewChat = startNewChat;
            }
            if (typeof window.showChatDetail !== 'function' && typeof showChatDetail === 'function') {
                window.showChatDetail = showChatDetail;
            }
        }

        // 等待初始化完成
        let attempts = 0;
        const maxAttempts = 40; // 2秒超时 (40 * 50ms)

        const checkInterval = setInterval(() => {
            attempts++;

            if (typeof startNewChat === 'function') {
                clearInterval(checkInterval);
                resolve();
            } else if (attempts >= maxAttempts) {
                console.error('聊天模块初始化超时');
                clearInterval(checkInterval);
                reject(new Error('聊天模块初始化超时'));
            }
        }, 50);
    });
}

// 调试函数：检查聊天模块状态
function debugChatModuleStatus() {
    // 调试函数已禁用
}

// 打开与联系人的聊天
function openChatWithContact(contact) {
    // 调试：检查当前状态
    debugChatModuleStatus();

    // 确保聊天模块已初始化
    if (typeof initChat === 'function' && typeof startNewChat !== 'function') {
        initChat();
    }

    // 使用聊天功能
    if (typeof startNewChat === 'function') {
        startNewChat(contact.id);
        switchToChatPage();
    } else {
        console.error('聊天功能不可用，使用备用方法');
        fallbackOpenChat(contact);
    }
}

// 切换到聊天页面的通用函数
function switchToChatPage() {
    // 切换到聊天页面
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => {
        page.classList.remove('active');
    });
    document.getElementById('chat-page').classList.add('active');

    // 更新底部导航栏激活状态
    const tabItems = document.querySelectorAll('.tab-item');
    tabItems.forEach(tab => {
        tab.classList.remove('active');
        if (tab.getAttribute('data-page') === 'chat-page') {
            tab.classList.add('active');
        }
    });
}

// 备用打开聊天方法
function fallbackOpenChat(contact) {

    // 将联系人信息存储到sessionStorage，让聊天页面读取
    sessionStorage.setItem('pendingChatContact', JSON.stringify({
        id: contact.id,
        name: contact.name,
        avatar: contact.avatar
    }));

    // 直接切换到聊天页面
    switchToChatPage();

    // 触发聊天页面的初始化
    setTimeout(() => {
        if (typeof initChat === 'function') {
            initChat();
        }

        // 再次尝试开始聊天
        setTimeout(() => {
            if (typeof startNewChat === 'function') {
                startNewChat(contact.id);
            } else {
                showToast('聊天功能暂时不可用，请刷新页面重试', 'error');
            }
        }, 500);
    }, 100);
}

// ==================== WebSocket 在线状态管理 ====================

// 存储通讯录的事件监听器引用，用于移除
let contactsOnlineHandler = null;
let contactsOfflineHandler = null;

// 初始化通讯录WebSocket监听器
function initContactsWebSocketListeners() {
    if (!window.wsManager) {
        setTimeout(initContactsWebSocketListeners, 1000);
        return;
    }

    // 先移除可能存在的旧监听器
    if (contactsOnlineHandler) {
        window.wsManager.off('onUserOnline', contactsOnlineHandler);
    }
    if (contactsOfflineHandler) {
        window.wsManager.off('onUserOffline', contactsOfflineHandler);
    }

    // 创建新的监听器函数
    contactsOnlineHandler = (data) => {
        updateContactOnlineStatus(data.user_id, true);
    };

    contactsOfflineHandler = (data) => {
        updateContactOnlineStatus(data.user_id, false);
    };

    // 监听用户上线事件
    window.wsManager.on('onUserOnline', contactsOnlineHandler);

    // 监听用户下线事件
    window.wsManager.on('onUserOffline', contactsOfflineHandler);
}

// 更新联系人在线状态
function updateContactOnlineStatus(userId, isOnline) {
    // 更新allContacts数组中的状态
    const contact = allContacts.find(c => c.id === userId);
    if (contact) {
        contact.isOnline = isOnline;
    }

    // 更新contacts数组中的状态
    contacts.forEach(group => {
        const contactInGroup = group.items.find(c => c.id === userId);
        if (contactInGroup) {
            contactInGroup.isOnline = isOnline;
        }
    });

    // 更新UI显示
    updateContactStatusInUI(userId, isOnline);
}

// 更新UI中的联系人状态显示
function updateContactStatusInUI(userId, isOnline) {
    const contactElements = document.querySelectorAll(`[data-contact-id="${userId}"]`);

    contactElements.forEach(element => {
        const statusDot = element.querySelector('.status-dot');
        const statusText = element.querySelector('.contact-status span');

        if (statusDot) {
            if (isOnline) {
                statusDot.classList.remove('offline');
            } else {
                statusDot.classList.add('offline');
            }
        }

        if (statusText) {
            statusText.textContent = isOnline ? '在线' : '离线';
        }
    });
}

// 批量更新在线状态
function updateMultipleContactsStatus(statusData) {
    Object.keys(statusData).forEach(userId => {
        const userIdNum = parseInt(userId);
        const status = statusData[userId];
        updateContactOnlineStatus(userIdNum, status.is_online);
    });
}

// 获取所有联系人的在线状态
function fetchContactsOnlineStatus() {
    if (!allContacts || allContacts.length === 0) {
        return;
    }

    const userIds = allContacts.map(contact => contact.id);

    fetch('/api/users/status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ user_ids: userIds })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            updateMultipleContactsStatus(data.data);
        }
    })
    .catch(error => {
        console.error('获取联系人在线状态失败:', error);
    });
}

// 初始化联系人在线状态
function initContactsOnlineStatus() {
    // 立即获取在线状态
    fetchContactsOnlineStatus();

    // 延迟再次获取在线状态，确保联系人列表已完全加载
    setTimeout(() => {
        fetchContactsOnlineStatus();
    }, 1000);

    // 定期更新在线状态（作为WebSocket的补充）
    setInterval(fetchContactsOnlineStatus, 30000); // 每30秒更新一次，更及时
}

// 在页面加载时初始化在线状态
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(initContactsOnlineStatus, 2000);
});

// 监听窗口大小变化，调整布局
window.addEventListener('resize', () => {
    // 防抖处理
    clearTimeout(window.resizeTimeout);
    window.resizeTimeout = setTimeout(() => {
        initSectionHeaderPosition();
    }, 100);
});

// 初始化通讯录头部按钮事件
function initContactsHeaderButtons() {
    // 使用延迟确保DOM元素完全加载
    setTimeout(() => {
        // 头部搜索切换按钮
        const contactsSearchToggle = document.getElementById('contacts-search-toggle');
        if (contactsSearchToggle) {
            // 移除可能存在的旧监听器
            contactsSearchToggle.removeEventListener('click', toggleContactsSearch);
            // 添加新的监听器
            contactsSearchToggle.addEventListener('click', toggleContactsSearch);

        } else {
            // 如果按钮不存在，再次尝试
            setTimeout(() => {
                const retryToggle = document.getElementById('contacts-search-toggle');
                if (retryToggle) {
                    retryToggle.removeEventListener('click', toggleContactsSearch);
                    retryToggle.addEventListener('click', toggleContactsSearch);

                }
            }, 500);
        }

        // 移除了添加联系人按钮，现在通过搜索框添加好友
    }, 100);
}

// 初始化分组标题位置
function initSectionHeaderPosition() {
    const searchSection = document.querySelector('.search-section');
    const sectionHeaders = document.querySelectorAll('.section-header');

    // 检查搜索区域是否隐藏（检查计算样式而不是内联样式）
    const isSearchHidden = searchSection &&
        (searchSection.style.display === 'none' ||
         getComputedStyle(searchSection).display === 'none');

    // 检查是否为移动端
    const isMobile = window.innerWidth <= 480;

    if (isSearchHidden) {
        // 搜索区域隐藏时，分组标题位置为只有头部的高度
        sectionHeaders.forEach(header => {
            header.style.top = isMobile ? '100px' : '120px';
        });
    } else {
        // 搜索区域显示时，分组标题位置为头部+搜索区域的高度
        sectionHeaders.forEach(header => {
            header.style.top = isMobile ? '180px' : '240px';
        });
    }
}
// 绑定外部点击与 ESC 键，自动隐藏搜索框（顶层定义）
function bindSearchAutoHide() {
    // 避免重复绑定
    unbindSearchAutoHide();

    const searchSection = document.querySelector('.search-section');
    if (!searchSection) return;

    // 点击页面任意非搜索区域处隐藏
    searchOutsideClickHandler = (e) => {
        const toggleBtn = document.getElementById('contacts-search-toggle');
        const isClickInside = searchSection.contains(e.target) || (toggleBtn && toggleBtn.contains(e.target));
        const isVisible = getComputedStyle(searchSection).display !== 'none';
        if (!isClickInside && isVisible) {
            e.preventDefault();
            e.stopPropagation();
            toggleContactsSearch();
        }
    };
    document.addEventListener('click', searchOutsideClickHandler);

    // ESC 关闭
    searchEscapeHandler = (e) => {
        if (e.key === 'Escape') {
            const isVisible = getComputedStyle(searchSection).display !== 'none';
            if (isVisible) toggleContactsSearch();
        }
    };
    document.addEventListener('keydown', searchEscapeHandler);

    // 移除“鼠标移出搜索区域隐藏”的行为，改为仅在点击搜索区域外时隐藏
    // 原逻辑已删除，防止在移动鼠标到搜索结果/添加好友按钮时提前关闭搜索框
}

// 解绑外部点击与 ESC 监听（顶层定义）
function unbindSearchAutoHide() {
    if (searchOutsideClickHandler) {
        document.removeEventListener('click', searchOutsideClickHandler);
        searchOutsideClickHandler = null;
    }
    if (searchEscapeHandler) {
        document.removeEventListener('keydown', searchEscapeHandler);
        searchEscapeHandler = null;
    }
    if (searchMouseLeaveHandler) {
        const el = document.querySelector('.search-section');
        if (el) el.removeEventListener('mouseleave', searchMouseLeaveHandler);
        searchMouseLeaveHandler = null;
    }
}



// 切换搜索区域显示/隐藏
function toggleContactsSearch() {
    const searchSection = document.querySelector('.search-section');
    const sectionHeaders = document.querySelectorAll('.section-header');

    if (searchSection) {
        if (searchSection.style.display === 'none' || !searchSection.style.display) {
            // 显示搜索区域
            searchSection.style.display = 'block';

            // 使用新的动画类
            setTimeout(() => {
                searchSection.classList.add('show');
            }, 10);



            // 调整分组标题的位置 - 头部 + 搜索区域
            const isMobile = window.innerWidth <= 480;
            sectionHeaders.forEach(header => {
                header.style.top = isMobile ? '180px' : '240px';
            });

            // 绑定外部点击与 ESC 键关闭
            bindSearchAutoHide();

            // 聚焦到搜索输入框（并清空上次内容）
            const searchInput = document.getElementById('contacts-search-input');
            if (searchInput) {
                searchInput.value = '';
                const clearBtn = document.getElementById('search-clear-btn');
                if (clearBtn) {
                    clearBtn.style.display = 'none';
                    clearBtn.classList.remove('show');
                }
                setTimeout(() => searchInput.focus(), 300);
            }
        } else {
            // 隐藏搜索区域
            searchSection.classList.remove('show');

            // 调整分组标题的位置 - 只有头部
            const isMobile = window.innerWidth <= 480;
            sectionHeaders.forEach(header => {
                header.style.top = isMobile ? '100px' : '120px';
            });

            // 立即清空搜索状态和内容
            searchQuery = '';
            filteredContacts = [];

            const searchInput = document.getElementById('contacts-search-input');
            if (searchInput) {
                searchInput.value = '';
                // 隐藏清除按钮
                const clearBtn = document.getElementById('search-clear-btn');
                if (clearBtn) {
                    clearBtn.style.display = 'none';
                    clearBtn.classList.remove('show');
                }
            }

            // 隐藏搜索结果提示和无结果提示
            const searchResultsInfo = document.getElementById('search-results-info');
            const noResults = document.getElementById('no-results');
            if (searchResultsInfo) searchResultsInfo.style.display = 'none';
            if (noResults) noResults.style.display = 'none';

            // 恢复显示好友列表
            applyFilters();

            // 等待过渡结束后真正隐藏并重新计算位置（更稳健）
            const onHideEnd = () => {
                searchSection.style.display = 'none';
                initSectionHeaderPosition();
            };
            // 监听一次过渡结束
            searchSection.addEventListener('transitionend', onHideEnd, { once: true });
            // 兜底：500ms后强制执行
            setTimeout(() => {
                onHideEnd();
            }, 500);
        }
    }
}

// 显示搜索区域（保留函数以防其他地方调用）
function showAddContactDialog() {
    // 显示搜索区域并聚焦到搜索框
    const searchSection = document.querySelector('.search-section');
    const searchInput = document.getElementById('contacts-search-input');

    if (searchSection && searchInput) {
        if (searchSection.style.display === 'none' || !searchSection.style.display) {
            toggleContactsSearch();
        }
        setTimeout(() => {
            searchInput.focus();
            searchInput.placeholder = '搜索用户名或微信号来添加好友...';
        }, 400);
    }

    showToast('请在搜索框中输入用户名或微信号来查找好友', 'info');
}

// 添加好友函数
function addFriend(friendId, friendName, buttonElement) {
    if (!friendId) {
        showToast('用户ID无效', 'error');
        return;
    }

    // 禁用按钮，防止重复点击
    buttonElement.disabled = true;
    buttonElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 添加中...';

    fetch('/api/friends/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ friend_id: friendId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            showToast(data.msg, 'success');
            // 更新按钮状态为"已添加"

            buttonElement.className = 'friend-status added';
            buttonElement.innerHTML = '已添加';
            buttonElement.disabled = true;

            // 更新搜索结果中的好友状态
            const contact = filteredContacts.find(c => c.id === friendId);
            if (contact) {
                contact.isFriend = true;
            }

            // 刷新好友列表
            setTimeout(() => {
                fetchContacts();
            }, 1000);
        } else {
            showToast(data.msg, 'error');
            // 恢复按钮状态
            buttonElement.disabled = false;
            buttonElement.innerHTML = '<i class="fas fa-user-plus"></i> 添加好友';
        }
    })
    .catch(error => {
        console.error('添加好友错误:', error);
        showToast('添加好友失败，请重试', 'error');
        // 恢复按钮状态
        buttonElement.disabled = false;
        buttonElement.innerHTML = '<i class="fas fa-user-plus"></i> 添加好友';
    });
}

// 显示删除好友菜单
function showDeleteFriendMenu(event, contact) {
    // 移除可能存在的旧菜单
    const existingMenu = document.querySelector('.delete-friend-menu');
    if (existingMenu) {
        existingMenu.remove();
    }

    // 创建右键菜单
    const menu = document.createElement('div');
    menu.className = 'delete-friend-menu';
    menu.style.position = 'fixed';
    menu.style.left = event.clientX + 'px';
    menu.style.top = event.clientY + 'px';
    menu.style.zIndex = '9999';
    menu.style.backgroundColor = '#fff';
    menu.style.border = '1px solid #ddd';
    menu.style.borderRadius = '8px';
    menu.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    menu.style.padding = '8px 0';
    menu.style.minWidth = '120px';

    const deleteItem = document.createElement('div');
    deleteItem.className = 'menu-item';
    deleteItem.style.padding = '8px 16px';
    deleteItem.style.cursor = 'pointer';
    deleteItem.style.color = '#ff4757';
    deleteItem.innerHTML = '<i class="fas fa-user-minus"></i> 删除好友';
    deleteItem.addEventListener('click', () => {
        menu.remove();
        confirmDeleteFriend(contact);
    });

    menu.appendChild(deleteItem);
    document.body.appendChild(menu);

    // 点击其他地方关闭菜单
    const closeMenu = (e) => {
        if (!menu.contains(e.target)) {
            menu.remove();
            document.removeEventListener('click', closeMenu);
        }
    };
    setTimeout(() => {
        document.addEventListener('click', closeMenu);
    }, 100);
}

// 全局变量存储要删除的好友信息
let deletingFriend = null;

// 确认删除好友 - 显示精美弹窗
function confirmDeleteFriend(contact) {
    deletingFriend = contact;

    // 设置好友名称
    const friendNameSpan = document.getElementById('friend-delete-name');
    if (friendNameSpan) {
        friendNameSpan.textContent = contact.name;
    }

    // 显示大厂风格确认弹窗
    const modal = document.getElementById('friend-delete-confirm-modal');
    if (modal) {
        modal.style.display = 'flex';
        // 强制重排
        modal.offsetHeight;
        setTimeout(() => {
            modal.classList.add('active');
        }, 10);
    }
}

// 隐藏删除好友确认弹窗
function hideFriendDeleteConfirmModal(immediate = false) {
    const modal = document.getElementById('friend-delete-confirm-modal');
    if (modal) {
        if (immediate) {
            // 立即关闭，用于按钮点击后
            modal.classList.add('closing');
            setTimeout(() => {
                modal.classList.remove('active', 'closing');
                modal.style.display = 'none';
            }, 250);
        } else {
            // 正常关闭动画
            modal.classList.remove('active');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 400);
        }
    }
    deletingFriend = null;
}

// 取消删除好友
function cancelDeleteFriend() {
    // 添加按钮点击反馈
    const cancelBtn = document.querySelector('.friend-delete-cancel-btn');
    if (cancelBtn) {
        cancelBtn.classList.add('clicked');
    }

    hideFriendDeleteConfirmModal(true);
}

// 确认删除好友（从弹窗）
function confirmDeleteFriendModal() {
    if (!deletingFriend) return;

    const friend = deletingFriend;

    // 添加按钮点击反馈
    const confirmBtn = document.querySelector('.friend-delete-confirm-btn');
    if (confirmBtn) {
        confirmBtn.classList.add('clicked');
    }

    // 快速关闭弹窗
    hideFriendDeleteConfirmModal(true);

    // 执行删除
    deleteFriend(friend.id, friend.name);
}

// 初始化删除好友确认弹窗事件监听
function initFriendDeleteModal() {
    const modal = document.getElementById('friend-delete-confirm-modal');
    if (modal) {
        // 点击背景关闭弹窗
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                hideFriendDeleteConfirmModal();
            }
        });

        // 阻止弹窗内容区域的点击事件冒泡
        const container = modal.querySelector('.friend-delete-confirm-container');
        if (container) {
            container.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        // ESC键关闭弹窗
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.classList.contains('active')) {
                hideFriendDeleteConfirmModal();
            }
        });
    }
}

// 删除好友函数
function deleteFriend(friendId, friendName) {
    // 前端检查：防止删除自己
    if (window.currentUser && friendId == window.currentUser.id) {
        showErrorToast('不能删除自己哦！', '操作提示');
        return;
    }

    fetch('/api/friends/remove', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ friend_id: friendId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            showSuccessToast(data.msg, '删除成功');
            // 刷新好友列表
            fetchContacts();
        } else {
            // 优化错误提示文案
            let errorMsg = data.msg;
            if (errorMsg === '不是好友关系') {
                errorMsg = '该用户不在您的好友列表中';
            }
            showErrorToast(errorMsg, '删除失败');
        }
    })
    .catch(error => {
        console.error('删除好友错误:', error);
        showErrorToast('网络连接失败，请检查网络后重试', '删除失败');
    });
}

// 为移动端添加滑动删除功能
function addSwipeDeleteFeature(contactElement, contact) {
    // 检测是否为触摸设备
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    if (!isTouchDevice) {
        return; // 非触摸设备不添加滑动功能
    }

    let startX = 0;
    let startY = 0;
    let currentX = 0;
    let isSwipeActive = false;
    let deleteButton = null;

    const SWIPE_THRESHOLD = 50; // 滑动阈值，超过此值显示删除按钮
    const DELETE_THRESHOLD = 120; // 删除阈值，超过此值直接删除
    const VERTICAL_THRESHOLD = 30; // 垂直移动阈值，超过此值取消滑动

    // 创建删除按钮
    function createDeleteButton() {
        if (deleteButton) return deleteButton;

        deleteButton = document.createElement('div');
        deleteButton.className = 'swipe-delete-button';
        deleteButton.innerHTML = '删除';
        deleteButton.style.cssText = `
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 80px;
            background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            border-radius: 0 16px 16px 0;
            z-index: 10;
        `;

        // 点击删除按钮
        deleteButton.addEventListener('click', (e) => {
            e.stopPropagation();
            confirmDeleteFriend(contact);
            resetSwipe();
        });

        return deleteButton;
    }

    // 重置滑动状态
    function resetSwipe() {
        if (deleteButton) {
            deleteButton.style.transform = 'translateX(100%)';
        }
        contactElement.style.transform = 'translateX(0)';
        contactElement.style.transition = 'transform 0.3s ease';
        isSwipeActive = false;

        // 移除删除按钮
        setTimeout(() => {
            if (deleteButton && deleteButton.parentNode) {
                deleteButton.parentNode.removeChild(deleteButton);
                deleteButton = null;
            }
        }, 300);
    }

    // 触摸开始
    contactElement.addEventListener('touchstart', (e) => {
        // 如果点击的是操作按钮区域，不处理滑动
        if (e.target.closest('.contact-actions')) {
            return;
        }

        const touch = e.touches[0];
        startX = touch.clientX;
        startY = touch.clientY;
        currentX = startX;

        // 清除过渡效果，准备滑动
        contactElement.style.transition = 'none';
    }, { passive: true });

    // 触摸移动
    contactElement.addEventListener('touchmove', (e) => {
        const touch = e.touches[0];
        const deltaX = touch.clientX - startX;
        const deltaY = Math.abs(touch.clientY - startY);
        currentX = touch.clientX;

        // 如果垂直移动过多，取消滑动
        if (deltaY > VERTICAL_THRESHOLD) {
            resetSwipe();
            return;
        }

        // 只处理向左滑动
        if (deltaX < -SWIPE_THRESHOLD) {
            e.preventDefault(); // 防止页面滚动

            if (!isSwipeActive) {
                isSwipeActive = true;
                // 确保联系人项有相对定位
                contactElement.style.position = 'relative';
                contactElement.style.overflow = 'hidden';

                // 添加删除按钮
                const button = createDeleteButton();
                contactElement.appendChild(button);

                // 显示删除按钮
                setTimeout(() => {
                    button.style.transform = 'translateX(0)';
                }, 10);
            }

            // 限制滑动距离
            const maxSwipe = Math.min(Math.abs(deltaX), 100);
            contactElement.style.transform = `translateX(-${maxSwipe}px)`;

            // 如果滑动距离超过删除阈值，添加删除提示
            if (Math.abs(deltaX) > DELETE_THRESHOLD) {
                contactElement.classList.add('swipe-delete-ready');
                if (navigator.vibrate) {
                    navigator.vibrate(30);
                }
            } else {
                contactElement.classList.remove('swipe-delete-ready');
            }
        } else if (deltaX > 10) {
            // 向右滑动，重置状态
            resetSwipe();
        }
    }, { passive: false });

    // 触摸结束
    contactElement.addEventListener('touchend', (e) => {
        const deltaX = currentX - startX;

        // 如果滑动距离超过删除阈值，直接删除
        if (Math.abs(deltaX) > DELETE_THRESHOLD && isSwipeActive) {
            confirmDeleteFriend(contact);
            resetSwipe();
        } else if (isSwipeActive) {
            // 否则保持删除按钮显示状态
            contactElement.style.transform = 'translateX(-90px)';
            contactElement.style.transition = 'transform 0.3s ease';
        }

        contactElement.classList.remove('swipe-delete-ready');
    }, { passive: true });

    // 触摸取消
    contactElement.addEventListener('touchcancel', (e) => {
        resetSwipe();
        contactElement.classList.remove('swipe-delete-ready');
    }, { passive: true });

    // 点击其他地方时重置滑动状态
    document.addEventListener('touchstart', (e) => {
        if (isSwipeActive && !contactElement.contains(e.target)) {
            resetSwipe();
        }
    }, { passive: true });
}

// 显示移动端操作提示
function showMobileTipsIfNeeded() {
    // 检测是否为移动设备或触摸设备
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const isSmallScreen = window.innerWidth <= 768;

    // 只在移动设备或小屏幕触摸设备上显示提示
    if ((isMobile || (isTouchDevice && isSmallScreen)) && contacts.length > 0) {
        const mobileTips = document.getElementById('mobile-tips');
        if (mobileTips) {
            // 检查是否已经显示过提示（使用localStorage记录）
            const hasShownTips = localStorage.getItem('contacts-mobile-tips-shown');

            if (!hasShownTips) {
                // 延迟显示提示，让用户先看到联系人列表
                setTimeout(() => {
                    mobileTips.style.display = 'block';

                    // 3秒后自动隐藏提示
                    setTimeout(() => {
                        mobileTips.style.display = 'none';
                        // 记录已显示过提示
                        localStorage.setItem('contacts-mobile-tips-shown', 'true');
                    }, 3000);
                }, 1000);
            }
        }
    }
}

// 重置通讯录初始化状态的全局函数
function resetContactsInitialization() {
    contactsInitialized = false;
    window.contactsInitialized = false;
}

// 导出全局函数
window.openChatWithContact = openChatWithContact;
window.clearSearch = clearSearch;
window.resetContactsInitialization = resetContactsInitialization;
// 初始化联系人下拉刷新功能
function initContactsPullToRefresh() {
    const contactsPage = document.getElementById('contacts-page');
    if (!contactsPage || !window.PullToRefresh) {
        return;
    }

    // 销毁之前的实例
    if (contactsPullToRefresh) {
        contactsPullToRefresh.destroy();
    }

    // 创建新的下拉刷新实例
    contactsPullToRefresh = new PullToRefresh(contactsPage, {
        threshold: 80,
        maxDistance: 120,
        resistance: 2.5,
        refreshText: '下拉刷新通讯录',
        releaseText: '松开刷新通讯录',
        loadingText: '正在刷新通讯录...',
        completeText: '通讯录刷新完成',
        onRefresh: () => {
            return new Promise((resolve) => {
                // 重新获取联系人数据
                fetch('/api/contacts', {
                    credentials: 'include'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        // 正确处理API返回的数据结构
                        window.currentUser = data.data.current_user;
                        processContactsData(data.data);
                    } else {
                        console.error('刷新通讯录失败:', data.msg);
                    }
                    resolve();
                })
                .catch(error => {
                    console.error('刷新通讯录错误:', error);
                    resolve();
                });
            });
        }
    });
}

window.updateContactOnlineStatus = updateContactOnlineStatus;
window.updateMultipleContactsStatus = updateMultipleContactsStatus;
window.fetchContactsOnlineStatus = fetchContactsOnlineStatus;
window.initContactsWebSocketListeners = initContactsWebSocketListeners;
window.initContactsHeaderButtons = initContactsHeaderButtons;
window.toggleContactsSearch = toggleContactsSearch;
window.showAddContactDialog = showAddContactDialog;
window.debugChatModuleStatus = debugChatModuleStatus; // 导出调试函数