(['C:\\Users\\<USER>\\Desktop\\wechat\\wechat_simple.py'],
 ['C:\\Users\\<USER>\\Desktop\\wechat'],
 ['pymysql', 'flask'],
 [('C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('css\\albums.css',
   'C:\\Users\\<USER>\\Desktop\\wechat\\css\\albums.css',
   'DATA'),
  ('css\\chat.css',
   'C:\\Users\\<USER>\\Desktop\\wechat\\css\\chat.css',
   'DATA'),
  ('css\\common.css',
   'C:\\Users\\<USER>\\Desktop\\wechat\\css\\common.css',
   'DATA'),
  ('css\\contacts.css',
   'C:\\Users\\<USER>\\Desktop\\wechat\\css\\contacts.css',
   'DATA'),
  ('css\\moments.css',
   'C:\\Users\\<USER>\\Desktop\\wechat\\css\\moments.css',
   'DATA'),
  ('css\\profile.css',
   'C:\\Users\\<USER>\\Desktop\\wechat\\css\\profile.css',
   'DATA'),
  ('css\\pull-to-refresh.css',
   'C:\\Users\\<USER>\\Desktop\\wechat\\css\\pull-to-refresh.css',
   'DATA'),
  ('images\\earth.png',
   'C:\\Users\\<USER>\\Desktop\\wechat\\images\\earth.png',
   'DATA'),
  ('index.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\index.html',
   'DATA'),
  ('js\\albums.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\albums.js',
   'DATA'),
  ('js\\chat.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\chat.js',
   'DATA'),
  ('js\\common.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\common.js',
   'DATA'),
  ('js\\contacts.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\contacts.js',
   'DATA'),
  ('js\\moments.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\moments.js',
   'DATA'),
  ('js\\profile.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\profile.js',
   'DATA'),
  ('js\\pull-to-refresh.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\pull-to-refresh.js',
   'DATA'),
  ('js\\utils.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\utils.js',
   'DATA'),
  ('js\\websocket.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\websocket.js',
   'DATA'),
  ('partials\\album-detail.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\partials\\album-detail.html',
   'DATA'),
  ('partials\\albums.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\partials\\albums.html',
   'DATA'),
  ('partials\\chat.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\partials\\chat.html',
   'DATA'),
  ('partials\\contacts.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\partials\\contacts.html',
   'DATA'),
  ('partials\\login-page.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\partials\\login-page.html',
   'DATA'),
  ('partials\\moments.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\partials\\moments.html',
   'DATA'),
  ('partials\\profile.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\partials\\profile.html',
   'DATA'),
  ('wechat_tables_only.sql',
   'C:\\Users\\<USER>\\Desktop\\wechat\\wechat_tables_only.sql',
   'DATA')],
 '3.13.0 (tags/v3.13.0:60403a5, Oct  7 2024, 09:38:07) [MSC v.1941 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('wechat_simple',
   'C:\\Users\\<USER>\\Desktop\\wechat\\wechat_simple.py',
   'PYSOURCE')],
 [('subprocess', 'D:\\Python\\Lib\\subprocess.py', 'PYMODULE'),
  ('selectors', 'D:\\Python\\Lib\\selectors.py', 'PYMODULE'),
  ('contextlib', 'D:\\Python\\Lib\\contextlib.py', 'PYMODULE'),
  ('signal', 'D:\\Python\\Lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Python\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Python\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Python\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Python\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Python\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Python\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client', 'D:\\Python\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\Python\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('gzip', 'D:\\Python\\Lib\\gzip.py', 'PYMODULE'),
  ('argparse', 'D:\\Python\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\Python\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\Python\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\Python\\Lib\\gettext.py', 'PYMODULE'),
  ('_compression', 'D:\\Python\\Lib\\_compression.py', 'PYMODULE'),
  ('xml.parsers.expat', 'D:\\Python\\Lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.parsers', 'D:\\Python\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'D:\\Python\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Python\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\Python\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('urllib.request', 'D:\\Python\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Python\\Lib\\ipaddress.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Python\\Lib\\fnmatch.py', 'PYMODULE'),
  ('getpass', 'D:\\Python\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\Python\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\Python\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\Python\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Python\\Lib\\mimetypes.py', 'PYMODULE'),
  ('getopt', 'D:\\Python\\Lib\\getopt.py', 'PYMODULE'),
  ('email.utils', 'D:\\Python\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email.charset', 'D:\\Python\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\Python\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('quopri', 'D:\\Python\\Lib\\quopri.py', 'PYMODULE'),
  ('email.errors', 'D:\\Python\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\Python\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\Python\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._parseaddr', 'D:\\Python\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'D:\\Python\\Lib\\calendar.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\Python\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'D:\\Python\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('urllib', 'D:\\Python\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('ssl', 'D:\\Python\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response', 'D:\\Python\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'D:\\Python\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('string', 'D:\\Python\\Lib\\string.py', 'PYMODULE'),
  ('hashlib', 'D:\\Python\\Lib\\hashlib.py', 'PYMODULE'),
  ('email', 'D:\\Python\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\Python\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email._policybase', 'D:\\Python\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\Python\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.message', 'D:\\Python\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\Python\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\Python\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Python\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\Python\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'D:\\Python\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Python\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Python\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header', 'D:\\Python\\Lib\\email\\header.py', 'PYMODULE'),
  ('bisect', 'D:\\Python\\Lib\\bisect.py', 'PYMODULE'),
  ('xml.sax', 'D:\\Python\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'D:\\Python\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Python\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader', 'D:\\Python\\Lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\Python\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('http.client', 'D:\\Python\\Lib\\http\\client.py', 'PYMODULE'),
  ('decimal', 'D:\\Python\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Python\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\Python\\Lib\\contextvars.py', 'PYMODULE'),
  ('numbers', 'D:\\Python\\Lib\\numbers.py', 'PYMODULE'),
  ('hmac', 'D:\\Python\\Lib\\hmac.py', 'PYMODULE'),
  ('struct', 'D:\\Python\\Lib\\struct.py', 'PYMODULE'),
  ('socket', 'D:\\Python\\Lib\\socket.py', 'PYMODULE'),
  ('tempfile', 'D:\\Python\\Lib\\tempfile.py', 'PYMODULE'),
  ('shutil', 'D:\\Python\\Lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'D:\\Python\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path', 'D:\\Python\\Lib\\zipfile\\_path\\__init__.py', 'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Python\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Python\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._local', 'D:\\Python\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('glob', 'D:\\Python\\Lib\\glob.py', 'PYMODULE'),
  ('pathlib._abc', 'D:\\Python\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('py_compile', 'D:\\Python\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Python\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'D:\\Python\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Python\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Python\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Python\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'D:\\Python\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Python\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Python\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('typing', 'D:\\Python\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc', 'D:\\Python\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Python\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Python\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\Python\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Python\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Python\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib._abc', 'D:\\Python\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Python\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Python\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Python\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Python\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('inspect', 'D:\\Python\\Lib\\inspect.py', 'PYMODULE'),
  ('token', 'D:\\Python\\Lib\\token.py', 'PYMODULE'),
  ('dis', 'D:\\Python\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Python\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata', 'D:\\Python\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('ast', 'D:\\Python\\Lib\\ast.py', 'PYMODULE'),
  ('json', 'D:\\Python\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\Python\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\Python\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\Python\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('__future__', 'D:\\Python\\Lib\\__future__.py', 'PYMODULE'),
  ('importlib.readers', 'D:\\Python\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Python\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Python\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'D:\\Python\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib.util', 'D:\\Python\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('tarfile', 'D:\\Python\\Lib\\tarfile.py', 'PYMODULE'),
  ('lzma', 'D:\\Python\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\Python\\Lib\\bz2.py', 'PYMODULE'),
  ('logging', 'D:\\Python\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'D:\\Python\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Python\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\Python\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\Python\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Python\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Python\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Python\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Python\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Python\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Python\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Python\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'D:\\Python\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.util', 'D:\\Python\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\Python\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Python\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Python\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Python\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Python\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes._endian', 'D:\\Python\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Python\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Python\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Python\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\Python\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Python\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Python\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Python\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Python\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\Python\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Python\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Python\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\Python\\Lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Python\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\Python\\Lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\Python\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('unittest.mock', 'D:\\Python\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest', 'D:\\Python\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\Python\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals', 'D:\\Python\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.main', 'D:\\Python\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner', 'D:\\Python\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.loader', 'D:\\Python\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.suite', 'D:\\Python\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'D:\\Python\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'D:\\Python\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('difflib', 'D:\\Python\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result', 'D:\\Python\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.util', 'D:\\Python\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('asyncio', 'D:\\Python\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Python\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\Python\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Python\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Python\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Python\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Python\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Python\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads', 'D:\\Python\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.taskgroups', 'D:\\Python\\Lib\\asyncio\\taskgroups.py', 'PYMODULE'),
  ('asyncio.subprocess', 'D:\\Python\\Lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.streams', 'D:\\Python\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\Python\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Python\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Python\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Python\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Python\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Python\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'D:\\Python\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('asyncio.trsock', 'D:\\Python\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered', 'D:\\Python\\Lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.timeouts', 'D:\\Python\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.tasks', 'D:\\Python\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.queues', 'D:\\Python\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.base_tasks', 'D:\\Python\\Lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\Python\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\Python\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto', 'D:\\Python\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.transports', 'D:\\Python\\Lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.protocols', 'D:\\Python\\Lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.futures', 'D:\\Python\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Python\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions', 'D:\\Python\\Lib\\asyncio\\exceptions.py', 'PYMODULE'),
  ('asyncio.events', 'D:\\Python\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Python\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines', 'D:\\Python\\Lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.constants', 'D:\\Python\\Lib\\asyncio\\constants.py', 'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig', 'D:\\Python\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\Python\\Lib\\_aix_support.py', 'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('platform', 'D:\\Python\\Lib\\platform.py', 'PYMODULE'),
  ('_ios_support', 'D:\\Python\\Lib\\_ios_support.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('site', 'D:\\Python\\Lib\\site.py', 'PYMODULE'),
  ('_pyrepl.main', 'D:\\Python\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl', 'D:\\Python\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl.curses', 'D:\\Python\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('curses', 'D:\\Python\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'D:\\Python\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'D:\\Python\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl.input', 'D:\\Python\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'D:\\Python\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.types', 'D:\\Python\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.commands', 'D:\\Python\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.pager', 'D:\\Python\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('tty', 'D:\\Python\\Lib\\tty.py', 'PYMODULE'),
  ('_pyrepl.historical_reader',
   'D:\\Python\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.reader', 'D:\\Python\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl._threading_handler',
   'D:\\Python\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'D:\\Python\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_colorize', 'D:\\Python\\Lib\\_colorize.py', 'PYMODULE'),
  ('_pyrepl.console', 'D:\\Python\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('code', 'D:\\Python\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Python\\Lib\\codeop.py', 'PYMODULE'),
  ('_pyrepl.trace', 'D:\\Python\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'D:\\Python\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'D:\\Python\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('ctypes.wintypes', 'D:\\Python\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('_pyrepl.unix_console',
   'D:\\Python\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'D:\\Python\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'D:\\Python\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.readline', 'D:\\Python\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'D:\\Python\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('rlcompleter', 'D:\\Python\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', 'D:\\Python\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', 'D:\\Python\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\Python\\Lib\\webbrowser.py', 'PYMODULE'),
  ('shlex', 'D:\\Python\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server', 'D:\\Python\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'D:\\Python\\Lib\\socketserver.py', 'PYMODULE'),
  ('html', 'D:\\Python\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\Python\\Lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics', 'D:\\Python\\Lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\Python\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser', 'D:\\Python\\Lib\\configparser.py', 'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib', 'D:\\Python\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'D:\\Python\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._types', 'D:\\Python\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tomllib._re', 'D:\\Python\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('tracemalloc', 'D:\\Python\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Python\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'D:\\Python\\Lib\\stringprep.py', 'PYMODULE'),
  ('_strptime', 'D:\\Python\\Lib\\_strptime.py', 'PYMODULE'),
  ('threading', 'D:\\Python\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'D:\\Python\\Lib\\_threading_local.py', 'PYMODULE'),
  ('random', 'D:\\Python\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\Python\\Lib\\statistics.py', 'PYMODULE'),
  ('fractions', 'D:\\Python\\Lib\\fractions.py', 'PYMODULE'),
  ('base64', 'D:\\Python\\Lib\\base64.py', 'PYMODULE'),
  ('uuid', 'D:\\Python\\Lib\\uuid.py', 'PYMODULE'),
  ('datetime', 'D:\\Python\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\Python\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('pymysql',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql.connections',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.charset',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.converters',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql._auth',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.times',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('pymysql.err',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('flask_cors',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask_cors\\__init__.py',
   'PYMODULE'),
  ('flask_cors.version',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask_cors\\version.py',
   'PYMODULE'),
  ('flask_cors.extension',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask_cors\\extension.py',
   'PYMODULE'),
  ('flask_cors.core',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask_cors\\core.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.http',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.test',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('markupsafe',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('flask_cors.decorator',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask_cors\\decorator.py',
   'PYMODULE'),
  ('flask',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.templating',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.scaffold',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\scaffold.py',
   'PYMODULE'),
  ('flask.wrappers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('jinja2',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('flask.signals',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('blinker',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker.base',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('blinker._utilities',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('flask.helpers',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.globals',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.sessions',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.json.tag',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('itsdangerous',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('werkzeug.local',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('flask.ctx',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.config',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.blueprints',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.app',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.testing',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('click.testing',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.core',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.shell_completion',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.parser',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.globals',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.exceptions',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.types',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('click.termui',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click.formatting',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._compat',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._winconsole',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('flask.logging',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.json.provider',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('click',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('flask.cli',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.typing',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.json',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE')],
 [('python313.dll', 'D:\\Python\\python313.dll', 'BINARY'),
  ('select.pyd', 'D:\\Python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\Python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\Python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\Python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\Python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\Python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\Python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\Python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\Python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'D:\\Python\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'D:\\Python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'D:\\Python\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'D:\\Python\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\Python\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'D:\\Python\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'D:\\Python\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\Python\\DLLs\\libffi-8.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\Python\\VCRUNTIME140_1.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('css\\albums.css',
   'C:\\Users\\<USER>\\Desktop\\wechat\\css\\albums.css',
   'DATA'),
  ('css\\chat.css',
   'C:\\Users\\<USER>\\Desktop\\wechat\\css\\chat.css',
   'DATA'),
  ('css\\common.css',
   'C:\\Users\\<USER>\\Desktop\\wechat\\css\\common.css',
   'DATA'),
  ('css\\contacts.css',
   'C:\\Users\\<USER>\\Desktop\\wechat\\css\\contacts.css',
   'DATA'),
  ('css\\moments.css',
   'C:\\Users\\<USER>\\Desktop\\wechat\\css\\moments.css',
   'DATA'),
  ('css\\profile.css',
   'C:\\Users\\<USER>\\Desktop\\wechat\\css\\profile.css',
   'DATA'),
  ('css\\pull-to-refresh.css',
   'C:\\Users\\<USER>\\Desktop\\wechat\\css\\pull-to-refresh.css',
   'DATA'),
  ('images\\earth.png',
   'C:\\Users\\<USER>\\Desktop\\wechat\\images\\earth.png',
   'DATA'),
  ('index.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\index.html',
   'DATA'),
  ('js\\albums.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\albums.js',
   'DATA'),
  ('js\\chat.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\chat.js',
   'DATA'),
  ('js\\common.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\common.js',
   'DATA'),
  ('js\\contacts.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\contacts.js',
   'DATA'),
  ('js\\moments.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\moments.js',
   'DATA'),
  ('js\\profile.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\profile.js',
   'DATA'),
  ('js\\pull-to-refresh.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\pull-to-refresh.js',
   'DATA'),
  ('js\\utils.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\utils.js',
   'DATA'),
  ('js\\websocket.js',
   'C:\\Users\\<USER>\\Desktop\\wechat\\js\\websocket.js',
   'DATA'),
  ('partials\\album-detail.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\partials\\album-detail.html',
   'DATA'),
  ('partials\\albums.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\partials\\albums.html',
   'DATA'),
  ('partials\\chat.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\partials\\chat.html',
   'DATA'),
  ('partials\\contacts.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\partials\\contacts.html',
   'DATA'),
  ('partials\\login-page.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\partials\\login-page.html',
   'DATA'),
  ('partials\\moments.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\partials\\moments.html',
   'DATA'),
  ('partials\\profile.html',
   'C:\\Users\\<USER>\\Desktop\\wechat\\partials\\profile.html',
   'DATA'),
  ('wechat_tables_only.sql',
   'C:\\Users\\<USER>\\Desktop\\wechat\\wechat_tables_only.sql',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('flask-2.3.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\entry_points.txt',
   'DATA'),
  ('flask-2.3.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('flask-2.3.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-2.3.3.dist-info\\LICENSE.rst',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\LICENSE.rst',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('flask-2.3.3.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\REQUESTED',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('flask-2.3.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\flask-2.3.3.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\wechat\\venv\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\wechat\\build\\WeChatApp\\base_library.zip',
   'DATA')],
 [('collections.abc', 'D:\\Python\\Lib\\collections\\abc.py', 'PYMODULE'),
  ('collections', 'D:\\Python\\Lib\\collections\\__init__.py', 'PYMODULE'),
  ('traceback', 'D:\\Python\\Lib\\traceback.py', 'PYMODULE'),
  ('heapq', 'D:\\Python\\Lib\\heapq.py', 'PYMODULE'),
  ('locale', 'D:\\Python\\Lib\\locale.py', 'PYMODULE'),
  ('linecache', 'D:\\Python\\Lib\\linecache.py', 'PYMODULE'),
  ('posixpath', 'D:\\Python\\Lib\\posixpath.py', 'PYMODULE'),
  ('_weakrefset', 'D:\\Python\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('warnings', 'D:\\Python\\Lib\\warnings.py', 'PYMODULE'),
  ('sre_parse', 'D:\\Python\\Lib\\sre_parse.py', 'PYMODULE'),
  ('types', 'D:\\Python\\Lib\\types.py', 'PYMODULE'),
  ('weakref', 'D:\\Python\\Lib\\weakref.py', 'PYMODULE'),
  ('functools', 'D:\\Python\\Lib\\functools.py', 'PYMODULE'),
  ('enum', 'D:\\Python\\Lib\\enum.py', 'PYMODULE'),
  ('abc', 'D:\\Python\\Lib\\abc.py', 'PYMODULE'),
  ('re._parser', 'D:\\Python\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'D:\\Python\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'D:\\Python\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'D:\\Python\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('keyword', 'D:\\Python\\Lib\\keyword.py', 'PYMODULE'),
  ('genericpath', 'D:\\Python\\Lib\\genericpath.py', 'PYMODULE'),
  ('stat', 'D:\\Python\\Lib\\stat.py', 'PYMODULE'),
  ('io', 'D:\\Python\\Lib\\io.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\Python\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec', 'D:\\Python\\Lib\\encodings\\uu_codec.py', 'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\Python\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8', 'D:\\Python\\Lib\\encodings\\utf_8.py', 'PYMODULE'),
  ('encodings.utf_7', 'D:\\Python\\Lib\\encodings\\utf_7.py', 'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\Python\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\Python\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32', 'D:\\Python\\Lib\\encodings\\utf_32.py', 'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\Python\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\Python\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16', 'D:\\Python\\Lib\\encodings\\utf_16.py', 'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\Python\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\Python\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620', 'D:\\Python\\Lib\\encodings\\tis_620.py', 'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\Python\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\Python\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\Python\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13', 'D:\\Python\\Lib\\encodings\\rot_13.py', 'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\Python\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\Python\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode', 'D:\\Python\\Lib\\encodings\\punycode.py', 'PYMODULE'),
  ('encodings.ptcp154', 'D:\\Python\\Lib\\encodings\\ptcp154.py', 'PYMODULE'),
  ('encodings.palmos', 'D:\\Python\\Lib\\encodings\\palmos.py', 'PYMODULE'),
  ('encodings.oem', 'D:\\Python\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'D:\\Python\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\Python\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\Python\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\Python\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\Python\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\Python\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\Python\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\Python\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\Python\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\Python\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\Python\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1', 'D:\\Python\\Lib\\encodings\\latin_1.py', 'PYMODULE'),
  ('encodings.kz1048', 'D:\\Python\\Lib\\encodings\\kz1048.py', 'PYMODULE'),
  ('encodings.koi8_u', 'D:\\Python\\Lib\\encodings\\koi8_u.py', 'PYMODULE'),
  ('encodings.koi8_t', 'D:\\Python\\Lib\\encodings\\koi8_t.py', 'PYMODULE'),
  ('encodings.koi8_r', 'D:\\Python\\Lib\\encodings\\koi8_r.py', 'PYMODULE'),
  ('encodings.johab', 'D:\\Python\\Lib\\encodings\\johab.py', 'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\Python\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\Python\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\Python\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\Python\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\Python\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\Python\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\Python\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\Python\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\Python\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\Python\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\Python\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\Python\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\Python\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\Python\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\Python\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\Python\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\Python\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\Python\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\Python\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\Python\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\Python\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\Python\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'D:\\Python\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'D:\\Python\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\Python\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\Python\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'D:\\Python\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312', 'D:\\Python\\Lib\\encodings\\gb2312.py', 'PYMODULE'),
  ('encodings.gb18030', 'D:\\Python\\Lib\\encodings\\gb18030.py', 'PYMODULE'),
  ('encodings.euc_kr', 'D:\\Python\\Lib\\encodings\\euc_kr.py', 'PYMODULE'),
  ('encodings.euc_jp', 'D:\\Python\\Lib\\encodings\\euc_jp.py', 'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\Python\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\Python\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950', 'D:\\Python\\Lib\\encodings\\cp950.py', 'PYMODULE'),
  ('encodings.cp949', 'D:\\Python\\Lib\\encodings\\cp949.py', 'PYMODULE'),
  ('encodings.cp932', 'D:\\Python\\Lib\\encodings\\cp932.py', 'PYMODULE'),
  ('encodings.cp875', 'D:\\Python\\Lib\\encodings\\cp875.py', 'PYMODULE'),
  ('encodings.cp874', 'D:\\Python\\Lib\\encodings\\cp874.py', 'PYMODULE'),
  ('encodings.cp869', 'D:\\Python\\Lib\\encodings\\cp869.py', 'PYMODULE'),
  ('encodings.cp866', 'D:\\Python\\Lib\\encodings\\cp866.py', 'PYMODULE'),
  ('encodings.cp865', 'D:\\Python\\Lib\\encodings\\cp865.py', 'PYMODULE'),
  ('encodings.cp864', 'D:\\Python\\Lib\\encodings\\cp864.py', 'PYMODULE'),
  ('encodings.cp863', 'D:\\Python\\Lib\\encodings\\cp863.py', 'PYMODULE'),
  ('encodings.cp862', 'D:\\Python\\Lib\\encodings\\cp862.py', 'PYMODULE'),
  ('encodings.cp861', 'D:\\Python\\Lib\\encodings\\cp861.py', 'PYMODULE'),
  ('encodings.cp860', 'D:\\Python\\Lib\\encodings\\cp860.py', 'PYMODULE'),
  ('encodings.cp858', 'D:\\Python\\Lib\\encodings\\cp858.py', 'PYMODULE'),
  ('encodings.cp857', 'D:\\Python\\Lib\\encodings\\cp857.py', 'PYMODULE'),
  ('encodings.cp856', 'D:\\Python\\Lib\\encodings\\cp856.py', 'PYMODULE'),
  ('encodings.cp855', 'D:\\Python\\Lib\\encodings\\cp855.py', 'PYMODULE'),
  ('encodings.cp852', 'D:\\Python\\Lib\\encodings\\cp852.py', 'PYMODULE'),
  ('encodings.cp850', 'D:\\Python\\Lib\\encodings\\cp850.py', 'PYMODULE'),
  ('encodings.cp775', 'D:\\Python\\Lib\\encodings\\cp775.py', 'PYMODULE'),
  ('encodings.cp737', 'D:\\Python\\Lib\\encodings\\cp737.py', 'PYMODULE'),
  ('encodings.cp720', 'D:\\Python\\Lib\\encodings\\cp720.py', 'PYMODULE'),
  ('encodings.cp500', 'D:\\Python\\Lib\\encodings\\cp500.py', 'PYMODULE'),
  ('encodings.cp437', 'D:\\Python\\Lib\\encodings\\cp437.py', 'PYMODULE'),
  ('encodings.cp424', 'D:\\Python\\Lib\\encodings\\cp424.py', 'PYMODULE'),
  ('encodings.cp273', 'D:\\Python\\Lib\\encodings\\cp273.py', 'PYMODULE'),
  ('encodings.cp1258', 'D:\\Python\\Lib\\encodings\\cp1258.py', 'PYMODULE'),
  ('encodings.cp1257', 'D:\\Python\\Lib\\encodings\\cp1257.py', 'PYMODULE'),
  ('encodings.cp1256', 'D:\\Python\\Lib\\encodings\\cp1256.py', 'PYMODULE'),
  ('encodings.cp1255', 'D:\\Python\\Lib\\encodings\\cp1255.py', 'PYMODULE'),
  ('encodings.cp1254', 'D:\\Python\\Lib\\encodings\\cp1254.py', 'PYMODULE'),
  ('encodings.cp1253', 'D:\\Python\\Lib\\encodings\\cp1253.py', 'PYMODULE'),
  ('encodings.cp1252', 'D:\\Python\\Lib\\encodings\\cp1252.py', 'PYMODULE'),
  ('encodings.cp1251', 'D:\\Python\\Lib\\encodings\\cp1251.py', 'PYMODULE'),
  ('encodings.cp1250', 'D:\\Python\\Lib\\encodings\\cp1250.py', 'PYMODULE'),
  ('encodings.cp1140', 'D:\\Python\\Lib\\encodings\\cp1140.py', 'PYMODULE'),
  ('encodings.cp1125', 'D:\\Python\\Lib\\encodings\\cp1125.py', 'PYMODULE'),
  ('encodings.cp1026', 'D:\\Python\\Lib\\encodings\\cp1026.py', 'PYMODULE'),
  ('encodings.cp1006', 'D:\\Python\\Lib\\encodings\\cp1006.py', 'PYMODULE'),
  ('encodings.cp037', 'D:\\Python\\Lib\\encodings\\cp037.py', 'PYMODULE'),
  ('encodings.charmap', 'D:\\Python\\Lib\\encodings\\charmap.py', 'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\Python\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\Python\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'D:\\Python\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\Python\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii', 'D:\\Python\\Lib\\encodings\\ascii.py', 'PYMODULE'),
  ('encodings.aliases', 'D:\\Python\\Lib\\encodings\\aliases.py', 'PYMODULE'),
  ('encodings', 'D:\\Python\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('_collections_abc', 'D:\\Python\\Lib\\_collections_abc.py', 'PYMODULE'),
  ('sre_constants', 'D:\\Python\\Lib\\sre_constants.py', 'PYMODULE'),
  ('sre_compile', 'D:\\Python\\Lib\\sre_compile.py', 'PYMODULE'),
  ('copyreg', 'D:\\Python\\Lib\\copyreg.py', 'PYMODULE'),
  ('operator', 'D:\\Python\\Lib\\operator.py', 'PYMODULE'),
  ('codecs', 'D:\\Python\\Lib\\codecs.py', 'PYMODULE'),
  ('reprlib', 'D:\\Python\\Lib\\reprlib.py', 'PYMODULE'),
  ('ntpath', 'D:\\Python\\Lib\\ntpath.py', 'PYMODULE'),
  ('re', 'D:\\Python\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('os', 'D:\\Python\\Lib\\os.py', 'PYMODULE')])
