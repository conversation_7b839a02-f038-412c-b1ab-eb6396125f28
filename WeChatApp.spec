# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['wechat_simple.py'],
    pathex=[],
    binaries=[],
    datas=[('css', 'css'), ('js', 'js'), ('images', 'images'), ('partials', 'partials'), ('index.html', '.'), ('wechat_tables_only.sql', '.')],
    hiddenimports=['pymysql', 'flask'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='WeChatApp',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
